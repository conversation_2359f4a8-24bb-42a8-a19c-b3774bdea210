@font-face {
  font-family: "Material Icons";
  font-style: normal;
  font-weight: 400;
  src:
    local("Material Icons"),
    local("MaterialIcons-Outlined"),
    url("/assets/fonts/material-symbol-outlined.woff2") format("woff2");
}

.material-symbols-outlined {
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  font-size: 24px; /* Preferred icon size */
  display: inline-block;
  line-height: 1;
  text-transform: none;
  letter-spacing: normal;
  word-wrap: normal;
  white-space: nowrap;
  direction: ltr;

  /* Support for all WebKit browsers. */
  -webkit-font-smoothing: antialiased;
  /* Support for Safari and Chrome. */
  text-rendering: optimizeLegibility;

  /* Support for Firefox. */
  -moz-osx-font-smoothing: grayscale;

  /* Support for IE. */
  font-feature-settings: "liga";
}

.material-icon--sm {
  font-size: 1.2em !important;
}

.material-icon--md {
  font-size: 1.6em !important;
}

.material-icon--lg {
  font-size: 3em !important;
}

.material-icon--fill {
  font-variation-settings: "FILL" 1;
}
