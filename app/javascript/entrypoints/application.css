@import url("../assets/stylesheets/input.css");
@import url("../assets/stylesheets/material-icons.css");
@import url("../assets/stylesheets/fonts.css");

@import "tailwindcss";
@config "./tailwind.config.js";

@theme {
  --color-background: oklch(0.9558 0.0235 var(--base-hue, 240));

  --color-primary-50: oklch(0.97 0.02 var(--primary-hue, 280));
  --color-primary-100: oklch(0.95 0.04 var(--primary-hue, 280));
  --color-primary-200: oklch(0.9 0.08 var(--primary-hue, 280));
  --color-primary-300: oklch(0.85 0.12 var(--primary-hue, 280));
  --color-primary-400: oklch(0.8 0.16 var(--primary-hue, 280));
  --color-primary-500: oklch(0.75 0.2 var(--primary-hue, 280));
  --color-primary-600: oklch(0.65 0.18 var(--primary-hue, 280));
  --color-primary-700: oklch(0.55 0.16 var(--primary-hue, 280));
  --color-primary-800: oklch(0.45 0.14 var(--primary-hue, 280));
  --color-primary-900: oklch(0.25 0.06 var(--primary-hue, 280));
  --color-primary-950: oklch(0.2 0.04 var(--primary-hue, 280));

  --color-accent-50: oklch(0.97 0.02 80);
  --color-accent-100: oklch(0.95 0.04 80);
  --color-accent-200: oklch(0.9 0.08 80);
  --color-accent-300: oklch(0.85 0.12 80);
  --color-accent-400: oklch(0.8 0.16 80);
  --color-accent-500: oklch(0.75 0.2 80);
  --color-accent-600: oklch(0.65 0.18 80);
  --color-accent-700: oklch(0.55 0.16 80);
  --color-accent-800: oklch(0.45 0.14 80);
  --color-accent-900: oklch(0.25 0.06 80);
  --color-accent-950: oklch(0.2 0.04 80);
}

@utility underline {
  text-decoration: underline;
  text-decoration-thickness: 2px;
  text-underline-offset: 1px;
  text-decoration-skip-ink: none;
}

html,
body {
  height: 100%;
  background-color: var(--color-background);
}

#app {
  height: 100%;
}
