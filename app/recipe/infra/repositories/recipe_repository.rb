module Recipe
  module Infra
    module Repositories
      class RecipeRepository
        def self.filter(filtering_params)
          results = Recipe::Infra::Models::RecipeModel.where(nil)

          filtering_params.each do |key, value|
            next if value.blank?

            case key.to_s
            when 'name'
              results = filter_by_name(results, value)
            when 'user_id'
              results = filter_by_user_id(results, value)
            when 'tags_ids'
              results = filter_by_tags_ids(results, value)
            end
          end

          results
        end

        private

        def self.filter_by_name(relation, name)
          relation.where("recipes.name LIKE ?", "%#{name}%")
        end

        def self.filter_by_user_id(relation, user_id)
          relation.where(user_id: user_id)
        end

        def self.filter_by_tags_ids(relation, tag_ids)
          # Convertir la chaîne séparée par des virgules en array
          tag_ids = tag_ids.is_a?(String) ? tag_ids.split(",").map(&:to_i) : Array(tag_ids)

          relation.joins(:tags)
            .where(tags: {id: tag_ids})
            .group("recipes.id")
            .having("COUNT(DISTINCT tags.id) = ?", tag_ids.size)
            .distinct
        end
      end
    end
  end
end
