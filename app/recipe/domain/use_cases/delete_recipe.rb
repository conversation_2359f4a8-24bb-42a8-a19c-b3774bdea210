module Recipe
  module Domain
    module UseCases
      class DeleteRecipe
        def initialize(repository: Recipe::Infra::Models::RecipeModel)
          @repository = repository
        end

        def call(id:)
          recipe = @repository.find(id)
          recipe.destroy!
          { success: true }
        rescue ActiveRecord::RecordNotFound
          { success: false, error: "Recipe not found" }
        rescue => e
          { success: false, error: e.message }
        end

        private

        attr_reader :repository
      end
    end
  end
end
