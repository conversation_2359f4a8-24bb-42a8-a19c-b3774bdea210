module Recipe
  module Domain
    module UseCases
      class CreateRecipe
        def initialize(repository: Recipe::Infra::Models::RecipeModel)
          @repository = repository
        end

        def call(attributes:, user:)
          recipe = @repository.new(attributes)
          recipe.user = user
          
          if recipe.save
            { success: true, recipe: recipe }
          else
            { success: false, errors: recipe.errors }
          end
        end

        private

        attr_reader :repository
      end
    end
  end
end
