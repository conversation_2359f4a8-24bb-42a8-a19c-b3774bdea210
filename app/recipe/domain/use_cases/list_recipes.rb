module Recipe
  module Domain
    module UseCases
      class ListRecipes
        def initialize(repository: Recipe::Infra::Repositories::RecipeRepository)
          @repository = repository
        end

        def call(filters: {}, includes: [], order: { created_at: :desc })
          recipes = @repository.filter(filters)
          recipes = recipes.includes(*includes) if includes.any?
          recipes = recipes.order(order) if order.any?
          recipes
        end

        private

        attr_reader :repository
      end
    end
  end
end
