module Recipe
  module Domain
    module Entities
      class Recipe
        include ActiveModel::Model
        include ActiveModel::Attributes
        include ActiveModel::Validations

        # Attributes
        attribute :id, :integer
        attribute :name, :string
        attribute :description, :string
        attribute :url, :string
        attribute :number_of_servings, :integer, default: 4
        attribute :difficulty, :integer, default: 0
        attribute :user_id, :integer
        attribute :created_at, :datetime
        attribute :updated_at, :datetime

        # Enums
        DIFFICULTIES = { easy: 0, medium: 1, hard: 2 }.freeze

        # Validations (Business Rules)
        validates :name, presence: true
        validates :number_of_servings, presence: true, numericality: { only_integer: true, greater_than: 0 }
        validates :difficulty, presence: true, inclusion: { in: DIFFICULTIES.values }

        # Business Logic Methods
        def difficulty_name
          DIFFICULTIES.key(difficulty) || :easy
        end

        def difficulty_name=(value)
          self.difficulty = DIFFICULTIES[value.to_sym] || 0
        end

        def easy?
          difficulty == DIFFICULTIES[:easy]
        end

        def medium?
          difficulty == DIFFICULTIES[:medium]
        end

        def hard?
          difficulty == DIFFICULTIES[:hard]
        end

        def serves_multiple?
          number_of_servings > 1
        end

        def has_url?
          url.present?
        end

        def has_description?
          description.present?
        end

        # Business validation methods
        def valid_difficulty?(diff)
          DIFFICULTIES.values.include?(diff)
        end

        def valid_servings?(servings)
          servings.is_a?(Integer) && servings > 0
        end
      end
    end
  end
end
