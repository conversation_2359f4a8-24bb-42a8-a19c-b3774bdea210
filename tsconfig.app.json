{
  "compilerOptions": {
    "composite": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    "baseUrl": "./",
    "paths": {
      "@layouts/*": ["./app/javascript/layouts/*"],
      "@pages/*": ["./app/javascript/pages/*"],
      "@components/*": ["./app/javascript/components/*"],
      "@customTypes/*": ["./app/javascript/types/*"],
      "@helpers/*": ["./app/javascript/helpers/*"],
      "@const/*": ["./app/javascript/const/*"],
      "@contexts/*": ["./app/javascript/contexts/*"],
      "@hooks/*": ["./app/javascript/hooks/*"],
      "@adapters/*": ["./app/javascript/adapters/*"]
    }
  },
  "include": ["app/javascript"]
}
