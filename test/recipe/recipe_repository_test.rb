require "test_helper"

class RecipeRepositoryTest < ActiveSupport::TestCase
  def setup
    @recipe = recipes(:one)
    @user = users(:one)
    @other_user = users(:no_relationship_user)
  end

  # Tests des méthodes de filtrage
  test "should filter by name" do
    results = RecipeRepository.filter({name: "ABC"})
    assert_equal results.to_a, []

    results = RecipeRepository.filter({name: "Gâteau"})
    assert_includes results, recipes(:one)
    assert_not_includes results, recipes(:two)
  end

  test "should filter by user_id" do
    user2 = users(:two)

    results = RecipeRepository.filter({user_id: user2.id})
    assert_includes results, recipes(:two)
    assert_not_includes results, @recipe
  end

  test "should filter by tags_ids" do
    tag1 = tags(:one)
    tag2 = tags(:two)

    # recipe1 is set with fixtures

    recipe2 = recipes(:two)
    recipe2.tags << tag2
    recipe2.tags << tag1
    recipe2.save!

    # Filtre par un seul tag
    results = RecipeRepository.filter({tags_ids: [tag1.id]})
    assert_includes results, @recipe
    assert_includes results, recipe2

    # Filtre par plusieurs tags (AND logic)
    results = RecipeRepository.filter({tags_ids: [tag1.id, tag2.id]})
    assert_includes results, recipe2
    assert_not_includes results, @recipe
  end

  test "should filter by tags_ids with string input" do
    tag1 = tags(:one)
    tag2 = tags(:two)

    recipe2 = recipes(:two)
    recipe2.tags << tag2
    recipe2.tags << tag1
    recipe2.save!

    # Test avec une chaîne séparée par des virgules
    results = RecipeRepository.filter({tags_ids: "#{tag1.id},#{tag2.id}"})
    assert_includes results, recipe2
    assert_not_includes results, @recipe
  end

  test "should handle multiple filters" do
    user2 = users(:two)
    
    # Test avec plusieurs filtres combinés
    results = RecipeRepository.filter({
      name: "Gâteau",
      user_id: @user.id
    })
    assert_includes results, @recipe
    assert_not_includes results, recipes(:two)
  end

  test "should handle empty filters" do
    results = RecipeRepository.filter({})
    assert_includes results, @recipe
    assert_includes results, recipes(:two)
  end

  test "should handle blank values" do
    results = RecipeRepository.filter({
      name: "",
      user_id: nil,
      tags_ids: []
    })
    assert_includes results, @recipe
    assert_includes results, recipes(:two)
  end
end
