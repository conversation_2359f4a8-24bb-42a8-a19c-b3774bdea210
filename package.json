{"private": true, "type": "module", "devDependencies": {"@types/lodash": "^4.17.16", "@types/node": "^22.15.18", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "vite": "^5.4.19", "vite-plugin-ruby": "^5.1.1"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@inertiajs/react": "^2.0.7", "@mui/material": "^7.0.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.3", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.3.4", "lodash": "^4.17.21", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.3", "typescript": "^5.8.3", "use-debounce": "^10.0.5", "use-file-picker": "^2.1.2", "uuid": "^11.1.0"}, "scripts": {"check": "tsc -p tsconfig.app.json && tsc -p tsconfig.node.json"}}